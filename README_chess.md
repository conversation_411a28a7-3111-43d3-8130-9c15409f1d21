# Chess Cards Battle

An innovative strategy game that combines traditional chess with card game mechanics, creating a unique tactical experience where players use special cards to modify the game state and create unexpected strategic opportunities.

## 🎮 Game Concept

Imagine if **Chess** met **Uno** and **Magic: The Gathering**! This game takes the classic chess board and pieces but adds:
- **Card Effects**: Play special cards to modify the game
- **Piece Health**: Pieces can survive multiple attacks
- **Energy System**: Manage resources to play powerful cards
- **Status Effects**: Shields, freezing, and other tactical modifiers

## ✨ Key Features

### 🏰 Enhanced Chess Mechanics
- Traditional chess pieces with standard movement rules
- **Health System**: Pieces have HP and can survive attacks
- **Status Effects**: Visual indicators for shields, freeze, damage
- **Multiple Win Conditions**: Checkmate, king destruction, or piece elimination

### 🃏 Comprehensive Card System
- **25+ Unique Cards** with diverse effects
- **5 Card Types**: Movement, Attack, Defense, Special, Wild
- **Energy Management**: 3 energy per turn to play cards
- **Deck Building**: Hand management with drawing and shuffling

### 🎯 Card Categories

#### Movement Cards 🏃‍♂️
- **Teleport**: Move any piece to any empty square
- **Extra Move**: Take an additional move this turn
- **Knight Jump**: Any piece can move like a knight
- **Diagonal/Straight Boost**: Unlimited movement in specific directions

#### Attack Cards ⚔️
- **Double Attack**: Deal 2 damage instead of 1
- **Pierce Defense**: Ignore shields and armor
- **Chain Attack**: Hit multiple adjacent pieces
- **Explosive Attack**: Damage all pieces in 3x3 area
- **Sniper Shot**: Attack any piece regardless of distance/obstacles

#### Defense Cards 🛡️
- **Shield**: Block the next attack completely
- **Reflect**: Damage attackers instead of taking damage
- **Fortress**: All your pieces gain +1 defense this turn
- **Sanctuary**: Target piece cannot be attacked this turn
- **Regenerate**: Heal 1 health point

#### Special Cards ✨
- **Swap Pieces**: Exchange positions of any two pieces
- **Freeze**: Prevent piece movement for 2 turns
- **Clone**: Create a copy of target piece (1 health)
- **Transform**: Change piece to different type
- **Time Warp**: Undo opponent's last move

#### Wild Cards 🌪️
- **Reverse Turn**: Opponent loses their next turn
- **Skip Turn**: Skip turn to draw 2 cards
- **Draw Cards**: Draw 3 additional cards
- **Steal Card**: Take random card from opponent
- **Chaos**: Random effect on random piece

## 🎲 How to Play

### Setup
1. Standard chess board with traditional piece placement
2. Each player starts with a shuffled deck of cards
3. Draw 5 cards to start
4. Begin with 3 energy per turn

### Turn Structure
1. **Start Phase**: Gain energy, draw 1 card
2. **Action Phase**: Make chess moves and/or play cards
3. **End Phase**: Pass turn to opponent

### Actions Per Turn
- **Make Chess Move**: Move pieces using traditional rules
- **Play Cards**: Spend energy to activate card effects
- **Combine Both**: Use cards to enhance your chess strategy

### Winning Conditions
- **Traditional Checkmate**: Trap the enemy king
- **King Destruction**: Reduce enemy king's health to 0
- **Total Elimination**: Destroy all enemy pieces

## 🛠️ Compilation & Running

### Requirements
- C++14 compatible compiler
- Standard C++ library

### Build Instructions

#### Using Makefile:
```bash
make -f Makefile_chess
```

#### Manual compilation:
```bash
g++ -std=c++14 -Wall -Wextra -O2 -o chess_cards chess_cards.cpp
```

#### For debugging:
```bash
g++ -std=c++14 -g -DDEBUG -o chess_cards chess_cards.cpp
```

### Running the Game
```bash
./chess_cards          # Linux/Mac
chess_cards.exe         # Windows
```

## 🎯 Strategic Depth

### Resource Management
- **Energy**: Carefully manage when to play expensive cards
- **Hand Size**: Balance between saving powerful cards and using them
- **Deck Knowledge**: Remember what cards have been played

### Tactical Considerations
- **Timing**: When to use defensive vs offensive cards
- **Positioning**: Set up pieces for card combinations
- **Adaptation**: React to opponent's card strategies
- **Risk Assessment**: Balance chess safety with card aggression

### Advanced Strategies
- **Card Synergy**: Combine multiple cards for powerful effects
- **Tempo Control**: Use cards to gain initiative
- **Resource Denial**: Steal or disrupt opponent's cards
- **Multi-layered Planning**: Think several turns ahead with both moves and cards

## 🏆 Game Statistics

- **Total Lines of Code**: 1,696 lines
- **Classes**: 15+ implemented classes
- **Card Effects**: 25+ unique card effects
- **Piece Types**: 6 traditional chess pieces with enhanced mechanics
- **Game Systems**: Chess movement, card effects, energy management, health system
- **Strategic Depth**: High - combines chess tactics with card game strategy

## 🎨 Technical Highlights

### Object-Oriented Design
- **Inheritance Hierarchy**: Clean piece class structure
- **Smart Pointers**: Modern C++ memory management
- **Template System**: Flexible card factory pattern
- **Polymorphism**: Extensible card effect system

### Game Architecture
- **Separation of Concerns**: Clear division between game logic and display
- **Extensible Design**: Easy to add new cards and effects
- **Robust Validation**: Comprehensive move and action validation
- **Error Handling**: Graceful handling of invalid inputs

## 🚀 Future Enhancements

Potential expansions for this game:
- **Campaign Mode**: Single-player story with AI opponents
- **Online Multiplayer**: Network play with matchmaking
- **Custom Decks**: Build your own card combinations
- **Tournament Mode**: Bracket-style competitions
- **AI Difficulty Levels**: Multiple computer opponents
- **Card Animations**: Visual effects for card plays
- **Sound Effects**: Audio feedback for moves and effects
- **Save/Load Games**: Resume matches later

## 📜 License

This project is open source and available under the MIT License.

---

**Chess Cards Battle** - Where traditional strategy meets modern card game innovation! 🏰🃏⚔️
