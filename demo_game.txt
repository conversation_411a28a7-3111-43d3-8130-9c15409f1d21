CHESS CARDS BATTLE - DEMO GAMEPLAY
===================================

This file shows a sample game session to demonstrate the unique mechanics
of Chess Cards Battle, where traditional chess meets card game strategy.

GAME START:
-----------
Player 1: <PERSON> (White pieces)
Player 2: <PERSON> (Black pieces)

Both players start with:
- Standard chess piece setup
- 5 cards in hand
- 3 energy per turn
- Shuffled deck of 25+ unique cards

TURN 1 - ALICE (WHITE):
-----------------------
Board: Standard chess starting position
Hand: [Shield, Extra Move, Double Attack, Teleport, Draw Cards]
Energy: 3/3

Alice's options:
1. Make normal chess move (e.g., e2-e4)
2. Play card + make move
3. Play multiple cards

<PERSON> chooses: Play "Extra Move" card (Cost: 2) + make two moves
- Plays Extra Move card (Energy: 3→1)
- Moves pawn e2-e4
- Uses extra move to advance d2-d4
- Ends turn

TURN 1 - BOB (BLACK):
---------------------
Board: <PERSON> has advanced two center pawns
Hand: [<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Jump, <PERSON>]
Energy: 3/3

<PERSON> chooses: Play "Knight Jump" + unusual move
- Plays Knight Jump card (Cost: 2, Energy: 3→1)
- Moves pawn a7 like a knight to b5! (normally impossible)
- This surprises Alice and controls key squares

TURN 2 - ALICE (WHITE):
-----------------------
Hand: [Shield, Double Attack, Teleport, Draw Cards, Fortress] (drew 1 new card)
Energy: 3/3

Alice sees Bob's advanced pawn and decides to eliminate it:
- Plays "Double Attack" card (Cost: 2, Energy: 3→1)
- Moves bishop to attack the b5 pawn
- Double Attack deals 2 damage instead of 1
- Bob's pawn is destroyed (normal pieces have 1 health)

TURN 3 - BOB (BLACK):
---------------------
Hand: [Freeze, Shield, Regenerate, Chaos, Swap Pieces] (drew 1 new card)
Energy: 3/3

Bob retaliates with a powerful combination:
- Plays "Swap Pieces" card (Cost: 3, Energy: 3→0)
- Swaps Alice's advanced bishop with his back-row rook
- Alice's bishop is now trapped behind Bob's pieces!
- This completely changes the board position

TURN 4 - ALICE (WHITE):
-----------------------
Hand: [Shield, Teleport, Draw Cards, Fortress, Pierce Defense]
Energy: 3/3

Alice needs to rescue her trapped bishop:
- Plays "Teleport" card (Cost: 3, Energy: 3→0)
- Teleports her bishop from the back rank to a central square
- Now the bishop is active again and threatens Bob's king

TURN 5 - BOB (BLACK):
---------------------
Hand: [Freeze, Shield, Regenerate, Chaos, Explosive Attack]
Energy: 3/3

Bob feels threatened and plays defensively:
- Plays "Shield" on his king (Cost: 1, Energy: 3→2)
- Plays "Freeze" on Alice's threatening bishop (Cost: 2, Energy: 2→0)
- Alice's bishop cannot move for 2 turns!

TURN 6 - ALICE (WHITE):
-----------------------
Hand: [Draw Cards, Fortress, Pierce Defense, Sanctuary, Clone]
Energy: 3/3

With her bishop frozen, Alice adapts her strategy:
- Plays "Fortress" card (Cost: 4, but she only has 3 energy - can't play)
- Instead plays "Draw Cards" (Cost: 1, Energy: 3→2)
- Draws 3 additional cards
- Makes a normal knight move to develop pieces

MID-GAME HIGHLIGHTS:
-------------------
- Bob plays "Explosive Attack" to damage multiple pieces in a 3x3 area
- Alice uses "Pierce Defense" to ignore Bob's shields
- Bob plays "Chaos" causing random effects on random pieces
- Alice uses "Clone" to duplicate her queen (clone has 1 health)
- Bob plays "Time Warp" to undo Alice's last move
- Alice plays "Steal Card" to take one of Bob's cards

LATE GAME CLIMAX:
-----------------
Turn 15 - Critical moment:
- Bob's king is in check from Alice's queen
- Bob has "Reverse Turn" card in hand
- Alice has "Sniper Shot" ready to finish the king

Bob plays "Reverse Turn" (Cost: 4):
- Alice loses her next turn completely
- Bob uses the extra time to move his king to safety
- The game continues with both players low on pieces

FINAL SEQUENCE:
---------------
Turn 18 - Alice has cornered Bob's damaged king (2/5 health)
- Alice plays "Sniper Shot" (Cost: 3)
- Targets Bob's king from across the board
- Deals 1 damage, reducing king to 1/5 health
- Bob's king survives but is critically wounded

Turn 19 - Bob's desperate counter:
- Plays "Transform" card (Cost: 5, uses all energy)
- Transforms his wounded king into a queen!
- Now has a mobile piece that can escape

Turn 20 - Alice's winning move:
- Plays "Chain Attack" (Cost: 4)
- Attacks Bob's transformed queen-king
- Chain effect hits adjacent pieces too
- Bob's last pieces are destroyed
- ALICE WINS!

FINAL RESULT:
-------------
Winner: Alice (White)
Victory Condition: Total piece elimination
Total Turns: 20
Cards Played: 15 by Alice, 12 by Bob

POST-GAME ANALYSIS:
------------------
Key Moments:
1. Bob's early "Knight Jump" surprised Alice
2. Alice's "Teleport" rescue was crucial
3. Bob's "Time Warp" delayed Alice's victory
4. The "Transform" gambit almost worked
5. Alice's final "Chain Attack" sealed the win

Strategic Lessons:
- Energy management is crucial
- Card timing can change everything
- Traditional chess tactics still matter
- Adaptation to card effects is key
- Resource conservation vs aggression balance

This game showcased how Chess Cards Battle creates entirely new
strategic possibilities while maintaining the core chess experience!
