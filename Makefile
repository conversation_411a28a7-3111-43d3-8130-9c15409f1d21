# Makefile for RPG Game

CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
TARGET = rpg_game
SOURCE = rpg_game.cpp

# Default target
all: $(TARGET)

# Build the game
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE)

# Clean build files
clean:
	rm -f $(TARGET) $(TARGET).exe *.sav

# Run the game
run: $(TARGET)
	./$(TARGET)

# Debug build
debug: CXXFLAGS += -g -DDEBUG
debug: $(TARGET)

.PHONY: all clean run debug
