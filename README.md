# Epic RPG Adventure

A comprehensive text-based role-playing game written in C++ with over 2000 lines of code.

## Features

### 🎮 Core Game Systems
- **Character System**: Stats, leveling, experience points
- **Combat System**: Turn-based combat with multiple actions
- **Inventory Management**: Item storage with capacity limits
- **Equipment System**: Weapons, armor, and consumables
- **Quest System**: Multiple quest types with rewards
- **World Exploration**: 5 interconnected locations
- **Shop System**: Buy and sell items
- **Save/Load**: Persistent game progress

### ⚔️ Combat Features
- Turn-based tactical combat
- Hit chance based on agility stats
- Critical hits and blocking mechanics
- Equipment durability system
- Multiple enemy types with AI behavior
- Random encounters while traveling

### 🎯 Quest Types
- **Kill Enemies**: Defeat specific monsters
- **Collect Items**: Find and gather items
- **Reach Locations**: Explore new areas
- **Talk to NPCs**: Interact with characters

### 🗺️ Game World
- **Village Square**: Starting location with shops
- **Dark Forest**: Dangerous woods with enemies
- **Goblin Cave**: Underground lair
- **Rocky Mountains**: Treacherous peaks
- **Ancient Dungeon**: End-game challenge area

## Compilation

### Requirements
- C++11 compatible compiler (g++, clang++, MSVC)
- Standard C++ library

### Build Instructions

#### Using Makefile (Linux/Mac/Windows with MinGW):
```bash
make
```

#### Manual compilation:
```bash
g++ -std=c++11 -Wall -Wextra -O2 -o rpg_game rpg_game.cpp
```

#### For debugging:
```bash
make debug
```

## How to Play

### Starting the Game
1. Run the executable: `./rpg_game` (Linux/Mac) or `rpg_game.exe` (Windows)
2. Enter your character name
3. Begin your adventure!

### Game Controls
The game uses a menu-driven interface. Simply enter the number corresponding to your choice.

### Main Menu Options
1. **Explore Current Location** - Search for enemies, items, and encounters
2. **Travel** - Move between connected locations
3. **View Character Status** - Check your stats and level
4. **View Inventory** - See your items and manage storage
5. **View Equipment** - Check equipped weapons and armor
6. **Manage Equipment** - Equip/unequip items
7. **View Quest Log** - Track your active quests
8. **Visit Shop** - Buy items (when available)
9. **Rest** - Restore health and mana
10. **Save Game** - Save your progress
11. **Load Game** - Load a saved game
0. **Quit** - Exit the game

### Combat System
During combat, you can:
- **Attack**: Deal damage to enemies
- **Defend**: Reduce incoming damage
- **Use Item**: Consume potions or other items
- **Run Away**: Attempt to flee (success based on agility)

### Tips for Success
- Explore all locations to find better equipment
- Complete quests for experience and gold rewards
- Buy health potions before venturing into dangerous areas
- Level up by defeating enemies and completing quests
- Save your game regularly
- Manage your equipment durability

## Game Statistics

- **Total Lines of Code**: 2000+
- **Classes**: 12 major classes
- **Game Systems**: 7 comprehensive systems
- **Locations**: 5 unique areas to explore
- **Enemy Types**: 5 different monsters
- **Item Categories**: Weapons, armor, potions, and misc items
- **Quest Objectives**: 4 different quest types

## Technical Details

### Architecture
The game is built using object-oriented design principles with the following main components:

- **Item System**: Base Item class with specialized Weapon, Armor, and Potion classes
- **Character System**: Player and Enemy classes with stats and abilities
- **World System**: Location and GameWorld classes for environment management
- **Combat System**: Dedicated CombatSystem class for battle mechanics
- **Quest System**: Quest class with progress tracking
- **Game Controller**: Main Game class orchestrating all systems

### Save System
Game saves are stored in `.sav` files containing:
- Player name and location
- Character stats and level
- Gold amount
- Current progress

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to fork this project and add your own features! Some ideas for expansion:
- Magic system with spells
- More enemy types and locations
- Crafting system
- Multiplayer support
- Graphics interface
- Sound effects

Enjoy your adventure! 🗡️⚔️🛡️
