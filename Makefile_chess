# Makefile for Chess Cards Battle

CXX = g++
CXXFLAGS = -std=c++14 -Wall -Wextra -O2
TARGET = chess_cards
SOURCE = chess_cards.cpp

# Default target
all: $(TARGET)

# Build the game
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE)

# Clean build files
clean:
	rm -f $(TARGET) $(TARGET).exe

# Run the game
run: $(TARGET)
	./$(TARGET)

# Debug build
debug: CXXFLAGS += -g -DDEBUG
debug: $(TARGET)

.PHONY: all clean run debug
